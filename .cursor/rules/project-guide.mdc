---
description:
globs:
alwaysApply: false
---
# Google AI Chat Bot with Web Browsing 项目指南

## 项目概述
这是一个基于 Google Gemini 2.0 Flash 的智能聊天机器人，集成了网页浏览功能，包含：
- Go 后端服务：提供 RESTful API 和浏览器自动化
- TypeScript React 前端：现代化聊天界面，支持多种交互模式

## 主要入口
- 后端入口：[main.go](mdc:main.go) - Go HTTP 服务器
- 前端入口：[frontend/src/main.tsx](mdc:frontend/src/main.tsx) - React 应用入口
- 浏览器服务：[pkg/browser/service.go](mdc:pkg/browser/service.go) - 网页浏览和内容提取

## 项目结构
- `main.go`: Go 后端服务，集成 Google AI 和浏览器功能
- `go.mod`: Go 依赖管理，包含 Playwright 等依赖
- `pkg/browser/service.go`: 浏览器服务模块，基于 Playwright
- `scripts/install_playwright.go`: Playwright 安装脚本
- `config/svc.yml`: 服务配置文件
- `frontend/`: TypeScript React 前端
  - `src/App.tsx`: 主聊天组件，支持多标签页
  - `src/App.css`: 现代化样式文件
  - `vite.config.ts`: Vite 构建配置，包含代理设置

## 主要功能
1. **智能对话** (`/api/chat`): 
   - 基于 Google Gemini 2.0 Flash
   - 自动检测需要网络信息的问题
   - 智能触发网络搜索

2. **网页浏览** (`/api/browse`):
   - 输入 URL，自动浏览网页
   - 智能内容提取和清理
   - 支持 JavaScript 渲染的页面

3. **网络搜索** (`/api/search`):
   - Google 搜索集成
   - 实时信息获取
   - 搜索结果内容提取

4. **健康检查** (`/api/health`):
   - 服务状态监控
   - 浏览器功能可用性检查

## 开发指南
1. **环境要求**：Go 1.21+, Node.js 16+, Google AI API Key
2. **安装浏览器**：`cd scripts && go run install_playwright.go`
3. **后端启动**：`go run main.go --port=8080`
4. **前端启动**：`cd frontend && npm run dev`
5. **环境变量**：必须设置 `GOOGLE_API_KEY`

## 技术栈
- **后端**：Go + Google Generative AI SDK + Playwright + CORS
- **前端**：React 18 + TypeScript + Vite + 多标签页界面
- **浏览器**：Playwright 无头浏览器，支持 Chromium/Firefox/WebKit
- **样式**：现代化 CSS + 响应式设计 + 动画效果

## 智能功能
- **自动网络搜索**：检测关键词（搜索、最新、新闻、价格等）自动触发搜索
- **URL 自动浏览**：消息中包含 URL 时自动浏览并提取内容
- **内容智能提取**：使用多种选择器提取网页主要内容
- **错误处理**：完善的错误处理和降级机制

## 注意事项
- 浏览器功能依赖 Playwright，首次使用需要安装
- Google 搜索可能遇到 CAPTCHA，这是正常现象
- 前端通过代理访问后端 API，避免 CORS 问题
- 支持实时聊天和移动端适配
